import type { ILocaleContext } from '@bika/contents/i18n/context';
import { OnlineUsersView } from '@bika/domains/auth/client/online-users-view';
import type { IRecordControlProps } from '@bika/domains/database/client/form/interface';
import { RecordFormComponent } from '@bika/domains/database/client/form/record-form-component';
import { LookupRecordContextProvider } from '@bika/domains/database/client/record-detail/lookup-record-context';
import { omit } from "lodash"
import { NodeVOAICopilotMenu } from '@bika/domains/editor/client/node-vo-menu/node-vo-ai-copilot-menu';
import { NodeHeaderTitle } from '@bika/domains/node/client/header/node-header-title-component';
import { useDatabaseVOContext } from '@bika/types/database/context';
import type { RecordCreate, RecordUpdate } from '@bika/types/database/dto';
import type { RecordDetailVO } from '@bika/types/database/vo';
import { useNodeResourceApiContext } from '@bika/types/node/context';
import type { FormUpdateBO, NodeUpdateDTOReqWithoutSpaceId } from '@bika/types/node/dto';
import type { FormVO, NodeDetailVO } from '@bika/types/node/vo';
import { useShareContext } from '@bika/types/space/context';
import { FromDataInputError, getRecordDTOFromRecordDetailVO } from '@bika/ui/form/utils';
import DatabaseOutlined from '@bika/ui/icons/components/datasheet_outlined';
import EditOutlined from '@bika/ui/icons/components/edit_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { DatabaseViewIconMap } from '@bika/ui/node/types-form/database-view-icons-map';
import { Skeleton } from '@bika/ui/skeleton';
import { useSnackBar } from '@bika/ui/snackbar';
import { Typography } from '@bika/ui/texts';
import { Tooltip } from '@bika/ui/tooltip';
import { copyText } from '@bika/ui/utils';
import { HeaderPageComponent } from '@bika/ui/web-layout';
import Button from '@mui/joy/Button';
import { useSize } from 'ahooks';
import _ from 'lodash';
import Link from 'next/link';
import qs from 'qs';
import React, { useCallback, useRef, useState } from 'react';
import { useUpdateEffect } from 'react-use';
import { TypesFormInputConfig } from '../../database/client/form/config';
import { NodeVOMenu } from '../../editor/client/node-vo-menu/node-vo-menu';
import { FormHeaderView } from './form-header-view';
import { FormSettingButton } from './form-setting';
import { useFormErrorStore } from './use-form-error-store';

interface FormDetailViewProps {
  value: NodeDetailVO;
  locale: ILocaleContext;
  isTemplatePreview?: boolean;
  params: {
    spaceId: string;
  };
}

export function FormNodeDetailVORenderer(props: FormDetailViewProps) {
  console.log('--- FormNodeDetailVORenderer rendered ---');
  const { value: data, params, isTemplatePreview } = props;
  const form = data?.resource as FormVO;

  const nodeApi = useNodeResourceApiContext();
  const databaseApi = useDatabaseVOContext();
  const { sharing } = useShareContext();
  const { toast } = useSnackBar();
  const [submitId, setSummitId] = useState(0);

  // Add ref and size observer for responsive tabs
  const formStackRef = useRef<HTMLDivElement>(null);
  const size = useSize(formStackRef);
  const shouldShowTabs = (size?.width ?? 0) >= 914;

  const handleFormReset = useCallback(() => {
    localStorage.removeItem(`node${form.id}`);
    setSummitId((prev) => prev + 1);
  }, [setSummitId, form.id]);

  const { isMutating, createRecord } = databaseApi.useRecordMutation();

  const resourceMutate = nodeApi.node.useNodeResourceBOMutation();

  const { t } = props.locale;

  const ref = useRef<IRecordControlProps>(null);
  const databaseId = form?.databaseId;

  const [record, setRecord] = React.useState<RecordDetailVO>(() => ({
    fields: form?.fields ?? [],
    record: {
      id: '',
      databaseId: form?.databaseId ?? '',
      revision: 0,
      cells: TypesFormInputConfig.getInitialValue(form?.fields),
    },
  }));

  useUpdateEffect(() => {
    setRecord({
      fields: form?.fields ?? [],
      record: {
        id: '',
        databaseId: form?.databaseId ?? '',
        revision: 0,
        cells: TypesFormInputConfig.getInitialValue(form?.fields),
      },
    });
  }, [form.view?.id, form?.fields]);

  React.useEffect(() => {
    const params = window.location.search.slice(1);
    const formRecord = localStorage.getItem(`node${form.id}`);

    if (formRecord && !params) {
      const newRecord: RecordDetailVO = {
        fields: form?.fields ?? [],
        revision: 0,
        record: JSON.parse(formRecord).record,
      };
      setRecord(newRecord);
    }
    if (params) {
      const query = qs.parse(params);

      const skipSidebar = omit(query, 'sidebar');

      const keys = Object.keys(skipSidebar)
      if(keys.length ===0) {
        return ;
      }
    
      const populateRecord: RecordDetailVO = {
        fields: form?.fields ?? [],
        record: {
          // @ts-expect-error type mismatch between FormRecordDetailVO and RecordDetailVO
          cells: query,
        },
      };
      setRecord(populateRecord);
    }
  }, []);

  const { errors, setError, onClear } = useFormErrorStore();

  const onChange = useCallback((newRecord: RecordDetailVO) => {
    localStorage.setItem(`node${form.id}`, JSON.stringify(newRecord));
    onClear();
    setRecord(newRecord);
  }, [form.id, onClear]);

  const handlePrePopulate = async () => {
    const queryString = qs.stringify(record.record?.cells);
    const url = `${window.location.origin}${window.location.pathname}?${queryString}`;
    await copyText(url);
    toast(t.copy.copy_link_to_clipboard, {
      variant: 'success',
    });
  };

  const onUpdate = useCallback(
    async (dto: FormVO) => {
      if (!form) {
        return;
      }
      const formData: FormUpdateBO = {
        resourceType: 'FORM',
        ...dto,
        cover: dto.cover ?? undefined,
        brandLogo: dto.brandLogo ?? undefined,
      };
      const updatedData: NodeUpdateDTOReqWithoutSpaceId = { data: formData, id: dto.id };

      await resourceMutate.update(updatedData);
    },
    [form, resourceMutate],
  );
  const debouncedUpdate = _.debounce(onUpdate, 1000);

  const doSubmit = async (submitData: RecordCreate | RecordUpdate) => {
    if (!databaseId) {
      return;
    }

    console.info('[doSubmit] submitData', submitData);
    // debugger;

    await createRecord({
      databaseId,
      formId: data.id,
      ...submitData,
      sharing,
    }).then(() => {
      ref.current?.reset();
      onClear();
      toast(t.resource.form.submitted_successfully, {
        variant: 'success',
      });
      handleFormReset();
      setRecord({
        fields: form?.fields ?? [],
        record: {
          id: '',
          databaseId: form?.databaseId ?? '',
          revision: 0,
          cells: TypesFormInputConfig.getInitialValue(form?.fields),
        },
      });
    });
  };

  const ViewIcon = DatabaseViewIconMap[form.view?.type || 'TABLE'];

  if (!form.id.startsWith('fom')) {
    return <Skeleton pos="NODE_PAGE" type={form.id} />;
  }

  return (
    <HeaderPageComponent
      className="hidden md:flex"
      header={
        <NodeHeaderTitle
          nodeType="FORM"
          nodeId={data.id}
          icon={{ kind: 'node-resource', customIcon: data.icon || undefined, nodeType: 'FORM' }}
          name={data.name}
          description={data.description}
          permission={!sharing ? data.permission?.privilege : undefined}
          tabs={
            !sharing &&
            !isTemplatePreview &&
            shouldShowTabs && (
              <Stack ml={3} display="flex" alignItems="center" direction="row">
                <Typography level="b4">{t.resource.form.link_to_resource}</Typography>
                <DatabaseOutlined color="var(--text-secondary)" />
                <Typography level="b4" ml={0.5}>
                  {form.database.name}
                </Typography>
                <Typography mx={0.5}>/</Typography>
                <Tooltip title={t.resource.form.click_to_view} arrow>
                  <Link
                    href={`/space/${params.spaceId}/node/${form.databaseId}/${form.view?.id}`}
                    className="flex items-center"
                  >
                    <ViewIcon color="var(--brand)" />
                    <Typography level="b4" textColor="var(--brand)" ml={0.5}>
                      {form.view?.name}
                    </Typography>
                  </Link>
                </Tooltip>
              </Stack>
            )
          }
          button={
            <Stack direction="row" alignItems="center" spacing={2}>
              <OnlineUsersView />
              {!sharing && (
                <Button
                  disabled={isTemplatePreview}
                  onClick={handlePrePopulate}
                  startDecorator={<EditOutlined currentColor />}
                >
                  {t.buttons.pre_fill_title_btn}
                </Button>
              )}
              {!isTemplatePreview && <FormSettingButton value={form} onChange={debouncedUpdate} />}
              {!isTemplatePreview && <NodeVOAICopilotMenu value={data} />}
              {!isTemplatePreview && <NodeVOMenu value={data} detail={data} />}
            </Stack>
          }
        />
      }
    >
      <Stack direction={'column'} ref={formStackRef}>
        <Stack
          className={'m-w-[1160px] items-center mx-auto border-rounded w-full'}
          id="form-node-detail-vo-renderer"
          sx={{
            maxWidth: '720px',
            borderRadius: '8px',
            overflow: 'hidden',
            marginTop: '32px',
            marginBottom: '40px',
            boxShadow: 'var(--shadow-high)',
            backgroundColor: 'var(--bg-surface)',
            paddingBottom: '40px',
          }}
        >
          {form && <FormHeaderView data={data} onUpdate={debouncedUpdate} api={nodeApi} />}
          <Box className={'w-full'} sx={{ px: 5 }}>
            <LookupRecordContextProvider>
              <RecordFormComponent
                key={submitId.toString()}
                // TODO get submit Id
                spaceId={params.spaceId}
                nodeId={form.databaseId}
                ref={ref}
                errors={errors}
                data={record as RecordDetailVO}
                onChange={onChange}
                isForm
                formId={form.id}
                disabled={isTemplatePreview}
              />
            </LookupRecordContextProvider>
            <Stack justifyContent={'center'} className="mt-[24px]">
              <Button
                size={'lg'}
                loading={isMutating()}
                disabled={isTemplatePreview}
                onClick={async () => {
                  if (isTemplatePreview) return;
                  let recordResult: RecordCreate | RecordUpdate | null = null;
                  try {
                    recordResult = getRecordDTOFromRecordDetailVO(
                      record as RecordDetailVO,
                      true,
                      props.locale,
                    );
                  } catch (e) {
                    if (e instanceof FromDataInputError) {
                      console.log(e.id); // 可访问自定义属性
                      const input = document.getElementById('form-node-detail-vo-renderer');
                      const gotInput = input?.querySelector(`#form_${e.id}`);
                      console.log('gotInput', gotInput);
                      if (gotInput) {
                        setError(e.id, e.message);
                        // get gotInput's previous sibling to the error message
                        const inputElement = gotInput.previousSibling as HTMLElement;
                        inputElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                      }

                      return;
                    }

                    toast((e as unknown as Error).message, {
                      variant: 'error',
                    });
                  }

                  if (!recordResult) {
                    return;
                  }
                  await doSubmit(recordResult);
                }}
              >
                {t.buttons.submit}
              </Button>
            </Stack>
          </Box>
        </Stack>
      </Stack>
    </HeaderPageComponent>
  );
}
