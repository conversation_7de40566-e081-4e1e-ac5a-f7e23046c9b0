'use client';

import { useLocale } from '@bika/contents/i18n/context';
import { RecordFormComponent } from '@bika/domains/database/client/form/record-form-component';
import { useSpaceModalContext } from '@bika/domains/space/client/modals/space-modals-context';
import { useLookupContext } from '@bika/types/database/context';
import type { ILookupVO, RecordDetailVO, RecordRenderVO } from '@bika/types/database/vo';
import { useCssColor } from '@bika/ui/colors';
import { RecordDetailAlertModal } from '@bika/ui/database/record-detail';
import { getFormDataFromRecord } from '@bika/ui/form/utils';
import { Box, Stack } from '@bika/ui/layouts';
import { Tab, TabList, TabPanel, Tabs } from '@bika/ui/tabs';
import { useSize } from 'ahooks';
import _ from 'lodash';
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
// eslint-disable-next-line import/no-cycle
import type { RecordDetailControlStatus } from './interface';
import { LookupFieldIdLookupDstIdSync } from './lookup-record-context';
import { RecordDetailLookupTabView } from './record-detail-lookup-tab-view';
import { useRecordDetailNavigation } from './use-record-detail-navigation';
import { isLinkOrOnewayLink } from './utils';
// eslint-disable-next-line import/no-cycle
import './record-detail-tabs.style.css';
// eslint-disable-next-line import/no-cycle
import { EllipsisText } from '@bika/ui/text';
import { Typography } from '@bika/ui/texts';

export interface IRecordDetailVOEditorProps {
  value: RecordDetailVO;
  onChange: (value: RecordDetailVO) => void;

  onSuccess?: (submitData: RecordRenderVO, lookupItem: ILookupVO) => void;
  status: RecordDetailControlStatus;
  setStatus: (_v: RecordDetailControlStatus) => void;
  // linkedNodeId?: string;
  linkedFieldId?: string;
  onUpdate?: () => void;
  recordId?: string;
  databaseId?: string;
  mirrorId?: string;
  viewId?: string;
  spaceId?: string;
}

function RecordDetailVOEditorBase(props: IRecordDetailVOEditorProps) {
  console.log('--- RecordDetailVOEditorBase rendered ---', props.value);
  const { linkedFieldId, recordId, spaceId, onUpdate, status, value, databaseId, onChange } = props;
  const locale = useLocale();
  const { t } = locale;

  const [modalVisible, setModalVisible] = useState(false);
  const onewayLinkFields = value.fields.filter((field) => isLinkOrOnewayLink(field.type));

  const { setRelatedColumns, getRelatedColumn } = useLookupContext();
  const magicFields = useMemo(
    () => value.fields.filter((field) => isLinkOrOnewayLink(field.type)),
    [value],
  );

  // keep learning

  const ref = useRef(null);
  const size = useSize(ref);

  let formOrientation: 'horizontal' | 'vertical' | undefined;
  if (size?.width) {
    formOrientation = size.width < 650 ? 'vertical' : 'horizontal';
  }
  /**
   * sync lookup value
   */
  useEffect(() => {
    const formData = getFormDataFromRecord(value);
    const listData = magicFields.map((field) => ({
      [field.id]: formData[field.id]?.value,
    }));

    const merged = listData.reduce((_previous, _current) => ({ ..._previous, ..._current }), {});
    const result = getRelatedColumn(recordId ?? '');
    if (_.isEmpty(result)) {
      setRelatedColumns(recordId ?? '', merged);
    }
  }, [
    getRelatedColumn,
    magicFields,
    value.record?.cells,
    databaseId,
    recordId,
    recordId,
    setRelatedColumns,
    value,
  ]);

  const { replace } = useRecordDetailNavigation();

  const setSelectedTag = useCallback(
    (newTagId: number) => {
      console.log('replace', newTagId);
      if (newTagId === 0) {
        replace({
          // linkedNodeId: '',
          linkedFieldId: '',
        });
      }
      const field = onewayLinkFields[newTagId - 1];
      if (field) {
        replace({
          // linkedNodeId: extractDstIdFromLookupField(field),
          linkedFieldId: field.id,
        });
      }
    },
    [replace, onewayLinkFields],
  );

  const selectedTag = useMemo(() => {
    if (linkedFieldId && linkedFieldId !== '') {
      const findIndex = magicFields.findIndex((field) => field.id === linkedFieldId);
      if (findIndex > -1) {
        return findIndex + 1;
      }
      return 0;
    }
    return 0;
  }, [magicFields, linkedFieldId]);

  const modalCtx = useSpaceModalContext();

  const colors = useCssColor();

  useEffect(() => {
    if (modalCtx) {
      modalCtx?.setWidth('70vw');
    }
    return () => {
      modalCtx?.setWidth(800);
    };
  }, [modalCtx]);

  return (
    <>
      <LookupFieldIdLookupDstIdSync magicFields={magicFields} fromDatabaseId={databaseId ?? ''} />

      {modalVisible && (
        <RecordDetailAlertModal
          onClose={() => {
            setModalVisible(false);
          }}
        />
      )}
      <Stack
        direction={'row'}
        className="flex space-between "
        sx={{
          overflowY: 'hidden',
          height: 'calc(80vh - 68px)',
        }}
      >
        <Box className="flex-auto overflow-y-hidden h-full flex ">
          <Tabs
            aria-label="Basic tabs"
            value={selectedTag}
            sx={{
              overflowY: 'hidden',
              width: '100%',
            }}
            className={'vika-record-form-tabs'}
            onChange={(_evt, value) => {
              const fieldTag = Number(value);
              const _tag = Number.isNaN(fieldTag) ? value : fieldTag;
              setSelectedTag(_tag as number);
            }}
          >
            <TabList
              className={'flex-none'}
              sx={{
                overflowX: 'auto', // 横向滚动
                flexWrap: 'nowrap', // 防止换行
                overflowY: 'hidden',
                visibility: onewayLinkFields.length === 0 ? 'hidden' : 'visible',
                display: onewayLinkFields.length === 0 ? 'none' : 'flex',
              }}
            >
              <Tab
                sx={{
                  minWidth: '120px',
                  maxWidth: '180px',
                }}
                value={0}
              >
                <EllipsisText>
                  <Typography textColor={'var(--brand)'}>{t.record.tab_general}</Typography>
                </EllipsisText>
              </Tab>
              {onewayLinkFields.map((field, index) => (
                <Tab
                  value={index + 1}
                  key={field.id}
                  sx={{
                    minWidth: '120px',
                    maxWidth: '180px',
                  }}
                >
                  <EllipsisText>
                    <Typography>{field.name?.toString()}</Typography>
                  </EllipsisText>
                </Tab>
              ))}
            </TabList>
            <TabPanel
              value={0}
              ref={ref}
              sx={{
                height: onewayLinkFields.length > 0 ? 'calc(100% - 30px)' : '100%',
                paddingX: 0,
                flex: '1 1 auto',
              }}
            >
              {formOrientation && (
                <RecordFormComponent
                  orientation={formOrientation}
                  disabled={status === 'VIEW'}
                  spaceId={spaceId}
                  nodeId={databaseId}
                  data={value}
                  onChange={onChange}
                />
              )}
            </TabPanel>
            {onewayLinkFields.map((field, index) => (
              <TabPanel
                value={index + 1}
                key={field.id}
                sx={{
                  height: onewayLinkFields.length > 0 ? 'calc(100% - 30px)' : '100%',
                  paddingX: 0,
                  flex: '1 1 auto',
                }}
              >
                <RecordDetailLookupTabView field={field} data={value} onUpdate={onUpdate} />
              </TabPanel>
            ))}
          </Tabs>
        </Box>
      </Stack>
    </>
  );
}

export const RecordDetailVOEditor = memo(RecordDetailVOEditorBase, (prevProps, nextProps) => {
  const areEqual = _.isEqual(prevProps.value, nextProps.value) && prevProps.status === nextProps.status;
  console.log('[RecordDetailVOEditor] memo check, areEqual:', areEqual);
  return areEqual;
});
