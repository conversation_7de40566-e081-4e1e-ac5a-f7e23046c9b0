'use client';

import { useSpaceContextForce } from '@bika/types/space/context';
import { useStackNavigatorContext } from '@bika/ui/stack-navigator';
import { useCallback, useMemo } from 'react';
import { useModalAsPageController } from './modal-as-page-provider';
import './record-detail-tabs.style.css';

export const useStackNavigateControl = () => {
  const ctx = useStackNavigatorContext();
  const spaceCtx = useSpaceContextForce();

  const { closeModal: closeModalPage } = useModalAsPageController();

  const closeModal = useCallback(() => {
    spaceCtx.showUIModal(null);
    closeModalPage?.();
  }, [closeModalPage, spaceCtx]);

  const backward = useCallback(() => {
    if (ctx && ctx.router.isTop()) {
      closeModal();
    }

    if (ctx) {
      ctx.router.pop();
    } else {
      closeModal();
    }
  }, [closeModal, ctx]);

  return useMemo(
    () => ({
      backward,
      closeModal,
    }),
    [backward, closeModal],
  );
};
