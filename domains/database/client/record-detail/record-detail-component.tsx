import { RecordFormComponent } from '@bika/domains/database/client/form/record-form-component';
import type { FieldVO, RecordDetailVO, RecordVO } from '@bika/types/database/vo';
import { useSpaceId } from '@bika/types/space/context';
import type React from 'react';

interface RecordVOInputProps {
  data: RecordVO;
  fieldList: FieldVO[];
}

const RecordVOComponent: React.FC<RecordVOInputProps> = ({ data, fieldList }) => {
  console.log('--- RecordVOComponent rendered ---');
  const { databaseId } = data;
  const spaceId = useSpaceId();

  const memoizedData = useMemo(() => ({
    record: data,
    revision: 0,
    fields: fieldList,
  }), [data, fieldList]);

  return (
    <RecordFormComponent
      orientation={'vertical'}
      disabled={true}
      spaceId={spaceId}
      nodeId={databaseId}
      // @ts-ignore: Type compatibility issue with RecordVOInputProps and RecordFormComponent data prop
      data={memoizedData as RecordDetailVO}
      onChange={() => {}}
    />
  );
};

export { RecordVOComponent };
