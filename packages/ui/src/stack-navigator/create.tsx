'use client';

import { useDeepCompareEffect } from 'ahooks';
import type React from 'react';
import { createContext, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { ButtonComponent } from '../button-component';
import ChevronLeftOutlined from '../icons/components/chevron_left_outlined';
import { Box } from '../layout-components';
import { useModalAsPageController } from '../modal-as-page';
import { Skeleton } from '../skeleton';
import {
  type HeaderBarBreadcumbs,
  useStackHeaderBar,
} from '../stack-header-bar/stack-header-bar-context';
import { StackNavigatorContext, useStackNavigatorContext } from './context';
import { useScreensReducer } from './screen-reducer';
import type { ScreenProps, StackRouter, StackScreenOptions } from './types';

type IRenderChildren = (screen: React.ReactNode) => React.ReactNode;

export interface NavigatorProps {
  // top Route
  initialRouteName: string;
  initialRouteParams?: Record<string, string>;
  children: React.ReactNode | IRenderChildren;
  onCancel?: () => void;
}

/**
 * Stack的全局内存ID，方便调试
 */
let stackIdGen = 0;

/**
 * T，可以设置type，比如string，number等，你自由地约束stack的key字符串类型
 * @returns
 */
export function createStackNavigator<PATH_TYPE extends string>() {
  // 局部context，用于screen向navigator注册
  interface InternalRegisterContextState {
    registerStackScreen: (_props: ScreenProps<PATH_TYPE>) => void;
    setStackOptions: (_opts: StackScreenOptions) => void;
  }

  const InternalRegisterContext = createContext<InternalRegisterContextState>(null!);

  // <Stack.Screen ...
  const Screen = (props: ScreenProps<PATH_TYPE>) => {
    const registerCtx = useContext(InternalRegisterContext);

    // 只注册一次
    useEffect(() => {
      registerCtx.registerStackScreen(props);
    }, []);
    return <> </>;
  };

  // <Stack.Navigator >，导航器
  const Navigator = (props: NavigatorProps) => {
    // 有哪些screens注册了？
    const [screensMap, dispatchScreensMap] = useScreensReducer();

    // 当前堆栈里
    const [stack, setStack] = useState<PATH_TYPE[]>([]);

    const stackNavigatorContext = useStackNavigatorContext();
    const modalContext = useModalAsPageController();

    const stackHeaderBar = useStackHeaderBar();

    const pushStack = (name: PATH_TYPE) => {
      setStack((prevArray) => {
        const newArray = [...prevArray];
        newArray.push(name);
        return newArray;
      });
    };

    /**
     * 注册一个stack screen
     * @param newProps
     * @param doPush 指注册的时候立刻推进去
     */
    const registerStackScreen = (newProps: ScreenProps<PATH_TYPE>) => {
      dispatchScreensMap({ type: 'REGISTER_SCREEN', screen: newProps });
    };

    const stackRouter: StackRouter = {
      history: stack,
      replace: (extraParams?: Record<string, string>) => {
        setStack((prevArray: PATH_TYPE[]) => {
          const path = stack?.[stack.length - 1] ?? '';
          const [route, queryString] = path.split('?');
          const parameters = new URLSearchParams(queryString ?? '');
          const params = new URLSearchParams(extraParams);
          const params1: Record<string, string> = {};

          for (const [key, value] of Array.from(parameters.entries())) {
            params1[key] = value;
          }
          for (const [key, value] of params.entries()) {
            params1[key] = value;
          }

          const nParams = new URLSearchParams(params1);
          return [
            ...prevArray.slice(0, prevArray.length - 1),
            `${route}?${nParams.toString()}` as PATH_TYPE,
          ];
        });
      },
      push: (name: string, extraParams?: Record<string, string>) => {
        const params = new URLSearchParams(extraParams);
        pushStack(`${name}?${params.toString()}` as PATH_TYPE);
      },
      pop: (extraParams?: Record<string, string>) => {
        // 必须保留一个，不能pop到0
        if (stack.length > 1) {
          // popStack();
          setStack((prevArray) => {
            const newArray = [...prevArray];
            newArray.pop();
            const head = newArray.pop();
            if (head && extraParams != null) {
              const [route, queryString] = head.split('?');
              const parameters = new URLSearchParams(queryString ?? '');
              const params = new URLSearchParams(extraParams);
              const params1: Record<string, string> = {};

              for (const [key, value] of parameters.entries()) {
                params1[key] = value;
              }
              for (const [key, value] of params.entries()) {
                params1[key] = value;
              }

              newArray.push(`${route}?${new URLSearchParams(params1).toString()}` as PATH_TYPE);
              return newArray;
            }
            newArray.push(head as PATH_TYPE);
            return newArray;
          });
        } else if (stackNavigatorContext != null && modalContext == null) {
          stackNavigatorContext.router.pop();
        } else {
          props.onCancel?.();
        }
      },
      previous: () => {
        const lastProp = stack[stack.length - 2];
        return lastProp;
      },
      isTop: () => {
        if (stackNavigatorContext != null && modalContext == null) {
          return false;
        }
        if (stack.length > 1) {
          return false;
        }
        return true;
      },
    };

    const current = useMemo(() => {
      const path = stack?.[stack.length - 1] ?? '';
      const [route, queryString] = path.split('?');
      const parameters = new URLSearchParams(queryString ?? '');
      const params: Record<string, string> = {};
      for (const [key, value] of parameters.entries()) {
        params[key] = value;
      }
      return {
        route,
        parameters: params,
      };
    }, [stack]);

    /**
     * 当前的stack
     */
    const currentScreenProps = useMemo(() => {
      const path = stack?.[stack.length - 1] ?? '';
      const [pathName] = path.split('?');
      const gStackProps = screensMap[pathName] ?? (() => <Box>{pathName} Not found</Box>);
      return gStackProps;
    }, [stack, screensMap]);

    const setStackOptions = (opts: StackScreenOptions) => {
      dispatchScreensMap({
        type: 'SET_OPTIONS',
        route: currentScreenProps.route,
        options: opts,
      });
    };

    /* 根据stack变化，改变stack header bar的标题栏、后退按钮 */
    useDeepCompareEffect(() => {
      const path = stack?.[stack.length - 1] ?? '';
      const [pathName] = path.split('?');
      const currentScreen = screensMap[pathName];

      if (currentScreen) {
        // 标题
        stackHeaderBar?.setTitle(currentScreen.initialTitle);

        // 左边按钮
        if (stackRouter.isTop()) {
          stackHeaderBar?.setLeftControls(null);
        } else {
          stackHeaderBar?.setLeftControls(
            <ButtonComponent
              variant="plain"
              color="neutral"
              onClick={() => stackRouter.pop()}
              startDecorator={<ChevronLeftOutlined color="var(--text-primary)" />}
            >
              Back
            </ButtonComponent>,
          );
        }
        // 默认没有right control按钮，screen可以自己设置
        const rightControls: React.ReactNode[] = [];
        if (currentScreen.options) {
          if (currentScreen.options.buttons) {
            let btnIndex = 0;
            for (const btn of currentScreen.options.buttons) {
              rightControls.push(
                <ButtonComponent key={btnIndex} onClick={btn.onClick}>
                  {btn.text}
                </ButtonComponent>,
              );

              btnIndex += 1;
            }
          }
        }
        stackHeaderBar?.setRightControls(<>{rightControls}</>);

        const breadcumbs: HeaderBarBreadcumbs[] = [];
        if (currentScreen.options?.breadcumbs === true) {
          for (let i = 0; i < stack.length; i += 1) {
            const pp = stack[i];
            const [ppathName] = pp.split('?');
            const pScreen = screensMap[ppathName];
            breadcumbs.push({
              text: pScreen.initialTitle!,
              url: '#',
            });
          }
        }
        stackHeaderBar?.setBreadcumbs(breadcumbs);
      }
    }, [stack, screensMap]);

    // 确保React的StrictMode第二次调用时，不会再push
    const initedRoute = useRef<boolean>(false);
    // 初始化，设置初始路由，第一个push
    useEffect(() => {
      if (!initedRoute.current) {
        const params = new URLSearchParams();
        if (props.initialRouteParams) {
          Object.entries(props.initialRouteParams).forEach(([key, value]) => {
            if (value != null && value !== '') {
              params.append(key, value);
            }
          });
        }
        pushStack(`${props.initialRouteName}?${params.toString()}` as PATH_TYPE);
        initedRoute.current = true;
      }
    }, [props.initialRouteName]);

    // ctx.AddButton，用来设置属性
    const Options = (opts: StackScreenOptions) => {
      const registerCtx = useContext(InternalRegisterContext);

      // 只注册一次
      useEffect(() => {
        registerCtx.setStackOptions(opts);
      }, []);

      return <></>;
    };
    console.log('ddddddd', currentScreenProps);

    return (
      <StackNavigatorContext.Provider
        value={{
          router: stackRouter,
          params: current.parameters,
          current: current.route,
          Options,
        }}
      >
        <InternalRegisterContext.Provider value={{ registerStackScreen, setStackOptions }}>
          {typeof props.children === 'function' ? (
            props.children(currentScreenProps?.component)
          ) : (
            <>
              {props.children}
              {currentScreenProps?.component}
              {currentScreenProps?.component == null && <Skeleton pos={'RECORD_DETAIL'} />}
            </>
          )}
        </InternalRegisterContext.Provider>
      </StackNavigatorContext.Provider>
    );
  };

  stackIdGen += 1;
  const id = stackIdGen;

  return {
    id,
    Screen,
    Navigator,
  };
}
